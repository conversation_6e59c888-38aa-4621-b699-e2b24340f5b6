# Blender Media Processor - Source Documentation

This directory contains the source code for the Blender Media Processor Agent, a comprehensive system for processing images and videos through Blender using IBM Watsonx Orchestrate.

## Directory Structure

```
src/
├── agents/
│   └── blender_agent.yaml          # Agent configuration and behavior
├── connections/
│   └── blender_connection.yaml     # Connection settings for Blender
└── tools/
    ├── server.py                   # Core Python tools and server logic
    └── __pycache__/               # Python bytecode cache
```

## Components Overview

### 1. Agent Configuration (`agents/blender_agent.yaml`)

**Purpose**: Defines the Blender Media Processor Agent's behavior, capabilities, and instructions.

**Key Features**:
- **Agent Identity**: `blender_media_processor_agent` using Llama-3-405B-Instruct model
- **Media Support**: Handles both images (JPG, PNG, BMP, TIFF) and videos (MP4, AVI, MOV, MKV, WEBM)
- **Core Capabilities**:
  - Load media files from file paths into Blender
  - Apply negative/inversion effects to images and videos
  - Change resolution of media content
  - List currently loaded media in Blender
  - Test Blender connectivity

**Workflow**:
1. Test Blender connection
2. Load media files into Blender
3. List available media
4. Apply effects or resize operations
5. Save processed files to Desktop

**Error Handling**: Comprehensive error handling for connection issues, media format compatibility, and processing failures.

### 2. Connection Configuration (`connections/blender_connection.yaml`)

**Purpose**: Manages connection parameters for communicating with Blender.

**Configuration**:
- **App ID**: `blender_apps`
- **Environment**: Draft environment with key-value configuration
- **Connection Parameters**:
  - `blender_host`: "*************" (Blender server IP)
  - `blender_port`: "9876" (Socket communication port)
  - `timeout`: "30" (Connection timeout in seconds)

### 3. Core Tools (`tools/server.py`)

**Purpose**: Implements the core functionality for communicating with Blender and processing media.

#### Key Functions:

##### Communication Layer
- **`send_to_blender_direct()`**: Direct socket communication with Blender MCP addon
- **`simple_blender_test()`**: Basic connectivity and functionality test

##### Media Processing Functions
- **`load_media_to_blender()`**: Load images/videos into Blender data blocks
- **`list_blender_media()`**: Enumerate all loaded media with metadata
- **`change_resolution()`**: Resize images/videos to specified dimensions
- **`apply_negative_effect_enhanced()`**: Apply color inversion effects

#### Watsonx Tool Decorators

All functions are wrapped with `@tool` decorators for IBM Watsonx Orchestrate integration:

1. **`test_blender_connection()`**: Test Blender connectivity
2. **`load_media_to_blender_tool()`**: Load media files
3. **`list_media_in_blender()`**: List loaded media
4. **`change_media_resolution()`**: Change media resolution
5. **`apply_negative_effect_to_media()`**: Apply negative effects

#### Technical Implementation

**Socket Communication**:
- Uses TCP sockets for real-time communication with Blender
- JSON-based command protocol
- Extended timeout handling for video processing (60 seconds)
- Robust error handling and connection management

**Image Processing**:
- Direct pixel manipulation for images
- Compositor nodes for advanced effects
- PNG output format with Desktop save location

**Video Processing**:
- Movie clip data blocks for video handling
- Video Sequence Editor for timeline operations
- Compositor-based effects with animation rendering
- MP4 output with H.264 encoding

**Cross-Platform Compatibility**:
- Path normalization for Windows/Unix systems
- Desktop detection with fallback to home directory
- File extension-based media type detection

## Usage Examples

### Command Line Interface

The server.py includes a CLI for direct testing:

```bash
# Test connection
python server.py test --host ***********

# Load media
python server.py load --path "/path/to/image.jpg" --host ***********

# List loaded media
python server.py list --host ***********

# Resize media
python server.py resize --name "image.jpg" --width 1920 --height 1080 --host ***********

# Apply negative effect
python server.py negative --name "video.mp4" --host ***********
```

### Agent Integration

The agent can be deployed in IBM Watsonx Orchestrate and will:
1. Accept natural language requests for media processing
2. Automatically determine whether input is image or video
3. Apply appropriate processing techniques
4. Provide detailed feedback on operations
5. Save results to user's Desktop

## Dependencies

- **Python Libraries**:
  - `socket`: Network communication
  - `json`: Data serialization
  - `logging`: Operation logging
  - `argparse`: CLI argument parsing
  - `os`, `time`: System utilities
  - `typing`: Type hints

- **IBM Watsonx Orchestrate**:
  - `ibm_watsonx_orchestrate.agent_builder.tools`: Tool decorators
  - `ibm_watsonx_orchestrate.run.connections`: Connection management
  - `ibm_watsonx_orchestrate.client.connections`: Connection types

- **External Requirements**:
  - Blender with MCP addon installed and running
  - Network connectivity to Blender host
  - File system access for media loading and saving

## Performance Considerations

- **Image Processing**: Typically fast (seconds)
- **Video Processing**: Time depends on length, resolution, and complexity
- **Large Files**: May require extended processing time
- **Network Latency**: Affects communication with remote Blender instances

## Error Handling

The system includes comprehensive error handling for:
- Connection failures (Blender not running, network issues)
- Media format incompatibility
- File not found errors
- Processing timeouts
- Invalid resolution parameters
- Memory limitations for large files

## Security Notes

- Direct socket communication requires network access
- File system access for loading and saving media
- No authentication mechanism (relies on network security)
- Blender code execution through socket interface
