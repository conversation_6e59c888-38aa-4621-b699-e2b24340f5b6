# # #!/usr/bin/env python3
# # # basic_contrast.py - Very simple contrast adjustment for 'input' image

# # import socket
# # import json

# # def send_to_blender(code):
# #     """Send Python code to Blender via MCP socket."""
# #     sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
# #     try:
# #         # Connect to the Blender MCP server
# #         sock.connect(('localhost', 9876))
        
# #         # Create the command
# #         command = {
# #             "type": "execute_blender_code",
# #             "params": {"code": code}
# #         }
        
# #         # Send the command
# #         sock.sendall(json.dumps(command).encode('utf-8') + b"\n")
        
# #         # Receive the response
# #         response_data = b""
# #         while True:
# #             chunk = sock.recv(4096)
# #             if not chunk or chunk.endswith(b"\n"):
# #                 response_data += chunk
# #                 break
# #             response_data += chunk
        
# #         # Parse and print the response
# #         response = json.loads(response_data.decode('utf-8'))
# #         print(f"Status: {response.get('status', 'unknown')}")
# #         print(f"Result: {response.get('result', '')}")
        
# #     finally:
# #         sock.close()

# # # Very simple contrast adjustment code
# # simple_code = """
# # import bpy

# # # Find the input image
# # input_img = bpy.data.images.get('input')

# # if input_img:
# #     print(f"Found image 'input' with dimensions {input_img.size[0]}x{input_img.size[1]}")
    
# #     # Create a simple scene with compositor
# #     bpy.context.scene.use_nodes = True
# #     tree = bpy.context.scene.node_tree
    
# #     # Clear existing nodes
# #     for node in tree.nodes:
# #         tree.nodes.remove(node)
    
# #     # Add input node
# #     input_node = tree.nodes.new('CompositorNodeImage')
# #     input_node.image = input_img
# #     input_node.location = (0, 0)
    
# #     # Add contrast node
# #     contrast_node = tree.nodes.new('CompositorNodeBrightContrast')
# #     contrast_node.inputs[2].default_value = 0.5  # Contrast
# #     contrast_node.location = (300, 0)
    
# #     # Add composite output
# #     output_node = tree.nodes.new('CompositorNodeComposite')
# #     output_node.location = (600, 0)
    
# #     # Connect nodes
# #     tree.links.new(input_node.outputs[0], contrast_node.inputs[0])
# #     tree.links.new(contrast_node.outputs[0], output_node.inputs[0])
    
# #     print("Successfully set up compositor nodes for contrast adjustment")
# #     print("Contrast has been set to 0.5 (reduced)")
# # else:
# #     print("Image 'input' not found")
# # """

# # # Send the code to Blender
# # print("Sending basic contrast adjustment code to Blender...")
# # send_to_blender(simple_code)

# #!/usr/bin/env python3
# # save_result.py - Render and save the contrast-adjusted image

# import socket
# import json

# def send_to_blender(code):
#     """Send Python code to Blender via MCP socket."""
#     sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
#     try:
#         # Connect to the Blender MCP server
#         sock.connect(('localhost', 9876))
        
#         # Create the command
#         command = {
#             "type": "execute_blender_code",
#             "params": {"code": code}
#         }
        
#         # Send the command
#         sock.sendall(json.dumps(command).encode('utf-8') + b"\n")
        
#         # Receive the response
#         response_data = b""
#         while True:
#             chunk = sock.recv(4096)
#             if not chunk or chunk.endswith(b"\n"):
#                 response_data += chunk
#                 break
#             response_data += chunk
        
#         # Parse and print the response
#         response = json.loads(response_data.decode('utf-8'))
#         print(f"Status: {response.get('status', 'unknown')}")
#         print(f"Result: {response.get('result', '')}")
        
#     finally:
#         sock.close()

# # Code to render and save the result
# save_code = """
# import bpy
# import os

# # Ensure we're in a scene with compositor nodes
# if not bpy.context.scene.use_nodes:
#     print("No compositor nodes set up. Make sure to run the contrast adjustment first.")
# else:
#     # Set up render settings
#     bpy.context.scene.render.resolution_x = 3926  # Match your image size
#     bpy.context.scene.render.resolution_y = 4907
#     bpy.context.scene.render.resolution_percentage = 100
#     bpy.context.scene.render.image_settings.file_format = 'PNG'
    
#     # Define output path - saving to desktop
#     desktop_path = os.path.expanduser("~/Desktop")
#     output_path = os.path.join(desktop_path, "input_contrast_adjusted.png")
#     bpy.context.scene.render.filepath = output_path
    
#     try:
#         # Render the image
#         print("Rendering image with adjusted contrast...")
#         bpy.ops.render.render(write_still=True)
        
#         print(f"Image saved to: {output_path}")
#     except Exception as e:
#         print(f"Error saving image: {str(e)}")
        
#         # Alternative method - try to save the Viewer Node image
#         try:
#             viewer = bpy.data.images.get('Viewer Node')
#             if viewer:
#                 viewer.filepath_raw = output_path
#                 viewer.file_format = 'PNG'
#                 viewer.save_render(output_path)
#                 print(f"Image saved using alternative method to: {output_path}")
#             else:
#                 print("Viewer Node image not found")
#         except Exception as e2:
#             print(f"Alternative saving method also failed: {str(e2)}")
# """

# # Send the code to Blender
# print("Sending code to save the contrast-adjusted image...")
# send_to_blender(save_code)

#!/usr/bin/env python3
# dramatic_adjustments.py - Apply dramatic image adjustments

#!/usr/bin/env python3
# bw_high_contrast.py - Apply dramatic black and white high contrast

#!/usr/bin/env python3
# negative_effect.py - Invert colors for negative effect

import socket
import json

def send_to_blender(code):
    """Send Python code to Blender via MCP socket."""
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        sock.connect(('9.109.230.178', 9876))
        command = {
            "type": "execute_blender_code",
            "params": {"code": code}
        }
        sock.sendall(json.dumps(command).encode('utf-8') + b"\n")
        response_data = b""
        while True:
            chunk = sock.recv(4096)
            if not chunk or chunk.endswith(b"\n"):
                response_data += chunk
                break
            response_data += chunk
        response = json.loads(response_data.decode('utf-8'))
        print(f"Status: {response.get('status', 'unknown')}")
        print(f"Result: {response.get('result', '')}")
        
    finally:
        sock.close()

negative_code = """
import bpy
import os

# Set up compositor
bpy.context.scene.use_nodes = True
tree = bpy.context.scene.node_tree

# Clear existing nodes
for node in tree.nodes:
    tree.nodes.remove(node)

# Add input node
input_node = tree.nodes.new('CompositorNodeImage')
img = bpy.data.images.get('input')
if img:
    input_node.image = img
input_node.location = (0, 0)

# 1. Invert colors
invert_node = tree.nodes.new('CompositorNodeInvert')
invert_node.invert_rgb = True
invert_node.location = (250, 0)

# Add output nodes
composite = tree.nodes.new('CompositorNodeComposite')
composite.location = (450, 0)

viewer = tree.nodes.new('CompositorNodeViewer')
viewer.location = (450, -100)

# Connect nodes
tree.links.new(input_node.outputs[0], invert_node.inputs[1])
tree.links.new(invert_node.outputs[0], composite.inputs[0])
tree.links.new(invert_node.outputs[0], viewer.inputs[0])

# Force render update
bpy.ops.render.render()

# Save the result
viewer_img = bpy.data.images.get('Viewer Node')
if viewer_img:
    desktop_path = os.path.expanduser("~/Desktop")
    output_path = os.path.join(desktop_path, "input_negative.png")
    
    try:
        # Set the file path and format
        viewer_img.filepath_raw = output_path
        viewer_img.file_format = 'PNG'
        
        # Save the image
        viewer_img.save_render(output_path)
        print(f"Negative image saved to: {output_path}")
        print("Applied invert colors effect (negative)")
    except Exception as e:
        print(f"Error saving image: {str(e)}")
else:
    print("Viewer Node image not found after rendering")
"""


print("Sending code to apply negative effect...")
send_to_blender(negative_code)