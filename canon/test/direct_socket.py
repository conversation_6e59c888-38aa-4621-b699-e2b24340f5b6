#!/usr/bin/env python3
# direct_socket.py - Direct socket connection to Blender MCP

import socket
import json
import sys
import os

def send_command(host='localhost', port=9876, command_type='ping', params=None):
    """Send a command to the Blender MCP server via direct socket connection."""
    if params is None:
        params = {}
    
    # Prepare the command
    command = {
        "type": command_type,
        "params": params
    }
    
    # Create socket connection
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    
    try:
        print(f"Connecting to {host}:{port}...")
        sock.connect((host, port))
        print("Connected!")
        
        # Send the command
        message = json.dumps(command).encode('utf-8') + b"\n"
        print(f"Sending: {command_type} with params: {params}")
        sock.sendall(message)
        
        # Receive response
        response_data = b""
        while True:
            chunk = sock.recv(4096)
            if not chunk:
                break
            response_data += chunk
            if chunk.endswith(b"\n"):
                break
        
        # Parse and return the response
        response = json.loads(response_data.decode('utf-8'))
        return response
    
    except Exception as e:
        print(f"Error: {str(e)}")
        return {"status": "error", "message": str(e)}
    
    finally:
        sock.close()

def import_image(image_path, name=None, host='localhost', port=9876):
    """Import an image into Blender using direct socket connection."""
    if not os.path.exists(image_path):
        print(f"Error: Image file not found: {image_path}")
        return False
    
    # If name is not provided, use the filename
    if name is None:
        name = os.path.splitext(os.path.basename(image_path))[0]
    
    # Use execute_blender_code command
    code = """
import bpy
import os

# Path to image
image_path = "{0}"

# Check if the image is already loaded
img = None
for existing_img in bpy.data.images:
    if existing_img.filepath == image_path or existing_img.name == "{1}":
        img = existing_img
        break

# If not loaded, load it
if img is None:
    img = bpy.data.images.load(image_path)
    img.name = "{1}"
else:
    # If already loaded, reload it
    img.reload()

print(f"Image '{{img.name}}' loaded successfully with dimensions {{img.size[0]}}x{{img.size[1]}}")
""".format(image_path.replace("\\", "/"), name)
    
    # Send the execute_blender_code command
    response = send_command(host, port, 'execute_blender_code', {"code": code})
    
    # Print the response
    print(f"Status: {response.get('status', 'unknown')}")
    print(f"Result: {response.get('result', '')}")
    
    return response.get('status') == 'success'

def main():
    if len(sys.argv) < 2:
        print("Usage: python direct_socket.py [command] [args...]")
        print("Commands:")
        print("  ping - Test connection to Blender MCP")
        print("  import [image_path] [name] - Import an image into Blender")
        return
    
    command = sys.argv[1]
    
    if command == 'ping':
        response = send_command()
        print(f"Response: {response}")
    
    elif command == 'import':
        if len(sys.argv) < 3:
            print("Usage: python direct_socket.py import [image_path] [name]")
            return
        
        image_path = sys.argv[2]
        name = sys.argv[3] if len(sys.argv) > 3 else None
        
        import_image(image_path, name)
    
    else:
        print(f"Unknown command: {command}")

if __name__ == "__main__":
    main()