# Modified addon.py for WatsonX Integration
# Based on the Blender MCP addon by <PERSON><PERSON><PERSON>: www.github.com/ahujasid
# Modified to work with IBM WatsonX API

import bpy
import mathutils
import json
import threading
import socket
import time
import requests
import tempfile
import traceback
import os
import shutil
import zipfile
from bpy.props import String<PERSON>roperty, Int<PERSON>roperty, <PERSON>ol<PERSON>roperty, EnumProperty
import io
from contextlib import redirect_stdout, suppress

bl_info = {
    "name": "Blender WatsonX MCP",
    "author": "Modified from BlenderMCP",
    "version": (1, 0),
    "blender": (3, 0, 0),
    "location": "View3D > Sidebar > BlenderWatsonX",
    "description": "Connect Blender to IBM WatsonX via MCP",
    "category": "Interface",
}

# Global variable to store server instance
server_instance = None

class BlenderWatsonXServer:
    def __init__(self, host='*************', port=9876):
        self.host = host
        self.port = port
        self.running = False
        self.socket = None
        self.server_thread = None
        
        # WatsonX API configuration
        self.api_key = ""
        self.project_id = ""
        self.url = "https://us-south.ml.cloud.ibm.com"  
        self.model_id = "meta-llama/llama-3-2-90b-vision-instruct"   
    
    def start(self):
        if self.running:
            return

        self.running = True
        self.server_thread = threading.Thread(target=self.run_server)
        self.server_thread.daemon = True
        self.server_thread.start()
    
    def stop(self):
        self.running = False
        if self.socket:
            self.socket.close()
    
    def run_server(self):
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            self.socket.bind((self.host, self.port))
            self.socket.listen(5)
            print(f"[BlenderWatsonX] MCP Server started on port {self.port}")
            
            while self.running:
                client_socket, addr = self.socket.accept()
                print(f"[BlenderWatsonX] New connection from {addr}")
                
                client_thread = threading.Thread(
                    target=self.handle_client,
                    args=(client_socket,)
                )
                client_thread.daemon = True
                client_thread.start()
                
        except Exception as e:
            print(f"[BlenderWatsonX] Server error: {str(e)}")
        finally:
            if self.socket:
                self.socket.close()
            print("[BlenderWatsonX] Server stopped")
            self.running = False
    
    def handle_client(self, client_socket):
        buffer_size = 4096
        data = b""
        
        try:
            while self.running:
                chunk = client_socket.recv(buffer_size)
                if not chunk:
                    break
                
                data += chunk
                
                try:
                    # Try to parse JSON from the received data
                    message = json.loads(data.decode('utf-8'))
                    
                    # Process the message
                    response = self.process_message(message)
                    
                    # Send the response back to the client
                    client_socket.sendall(json.dumps(response).encode('utf-8') + b"\n")
                    
                    # Reset the buffer
                    data = b""
                except json.JSONDecodeError:
                    # If we received incomplete JSON, continue receiving data
                    continue
                except Exception as e:
                    # In case of other exceptions, send error response
                    error_response = {"status": "error", "message": str(e)}
                    client_socket.sendall(json.dumps(error_response).encode('utf-8') + b"\n")
                    
                    # Reset the buffer
                    data = b""
        except Exception as e:
            print(f"[BlenderWatsonX] Error handling client: {str(e)}")
        finally:
            client_socket.close()
    
    def process_message(self, message):
        message_type = message.get("type")
        params = message.get("params", {})
        
        try:
            # Handle different message types
            if message_type == "ping":
                return {"status": "success", "result": "pong"}
            
            elif message_type == "describe_tools":
                return self.describe_tools()
            
            elif message_type == "get_scene_info":
                return self.get_scene_info()
                
            elif message_type == "execute_blender_code":
                code = params.get("code", "")
                return self.execute_blender_code(code)
                
            elif message_type == "create_object":
                obj_type = params.get("object_type", "")
                location = params.get("location", [0, 0, 0])
                size = params.get("size", 1.0)
                return self.create_object(obj_type, location, size)
                
            elif message_type == "delete_object":
                obj_name = params.get("object_name", "")
                return self.delete_object(obj_name)
                
            elif message_type == "set_material":
                obj_name = params.get("object_name", "")
                color = params.get("color", [0.8, 0.8, 0.8, 1.0])
                return self.set_material(obj_name, color)
            
            elif message_type == "watsonx_query":
                prompt = params.get("prompt", "")
                return self.watsonx_query(prompt)
            
            else:
                return {"status": "error", "message": f"Unknown message type: {message_type}"}
                
        except Exception as e:
            traceback.print_exc()
            return {"status": "error", "message": str(e)}

    def describe_tools(self):
        tools = [
            {
                "name": "get_scene_info",
                "description": "Get information about the current scene in Blender, including objects, materials, and basic properties.",
                "params": {}
            },
            {
                "name": "execute_blender_code",
                "description": "Execute arbitrary Python code in Blender. This allows for full control over the Blender environment using the bpy API.",
                "params": {
                    "code": {
                        "type": "string",
                        "description": "The Python code to execute."
                    }
                }
            },
            {
                "name": "create_object",
                "description": "Create a new object in the scene.",
                "params": {
                    "object_type": {
                        "type": "string", 
                        "description": "Type of object to create (cube, sphere, plane, cylinder, cone, torus, monkey)",
                    },
                    "location": {
                        "type": "array",
                        "description": "Location of the object as [x, y, z] coordinates"
                    },
                    "size": {
                        "type": "number",
                        "description": "Size of the object"
                    }
                }
            },
            {
                "name": "delete_object",
                "description": "Delete an object from the scene.",
                "params": {
                    "object_name": {
                        "type": "string",
                        "description": "Name of the object to delete."
                    }
                }
            },
            {
                "name": "set_material",
                "description": "Set material properties for an object.",
                "params": {
                    "object_name": {
                        "type": "string",
                        "description": "Name of the object."
                    },
                    "color": {
                        "type": "array",
                        "description": "RGBA color values as [r, g, b, a], each in range 0.0-1.0."
                    }
                }
            },
            {
                "name": "watsonx_query",
                "description": "Send a query to WatsonX API",
                "params": {
                    "prompt": {
                        "type": "string",
                        "description": "The prompt to send to WatsonX"
                    }
                }
            }
        ]
        
        return {"status": "success", "result": tools}
    
    def get_scene_info(self):
        scene_info = {
            "objects": []
        }
        
        for obj in bpy.context.scene.objects:
            obj_info = {
                "name": obj.name,
                "type": obj.type,
                "location": [obj.location.x, obj.location.y, obj.location.z]
            }
            
            if obj.type == 'MESH':
                obj_info["vertices"] = len(obj.data.vertices)
                obj_info["polygons"] = len(obj.data.polygons)
            
            scene_info["objects"].append(obj_info)
        
        return {"status": "success", "result": scene_info}
    
    def execute_blender_code(self, code):
        output = io.StringIO()
        
        try:
            with redirect_stdout(output):
                exec(code, {"bpy": bpy, "mathutils": mathutils})
            
            return {"status": "success", "result": output.getvalue()}
        except Exception as e:
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def create_object(self, obj_type, location, size):
        try:
            # Convert inputs
            if isinstance(location, list) and len(location) == 3:
                location = tuple(location)
            else:
                location = (0, 0, 0)
            
            # Create object based on type
            if obj_type.lower() == "cube":
                bpy.ops.mesh.primitive_cube_add(size=size, location=location)
            elif obj_type.lower() == "sphere":
                bpy.ops.mesh.primitive_uv_sphere_add(radius=size/2, location=location)
            elif obj_type.lower() == "plane":
                bpy.ops.mesh.primitive_plane_add(size=size, location=location)
            elif obj_type.lower() == "cylinder":
                bpy.ops.mesh.primitive_cylinder_add(radius=size/2, depth=size, location=location)
            elif obj_type.lower() == "cone":
                bpy.ops.mesh.primitive_cone_add(radius1=size/2, depth=size, location=location)
            elif obj_type.lower() == "torus":
                bpy.ops.mesh.primitive_torus_add(location=location)
            elif obj_type.lower() == "monkey":
                bpy.ops.mesh.primitive_monkey_add(size=size, location=location)
            else:
                return {"status": "error", "message": f"Unknown object type: {obj_type}"}
            
            # Get the created object
            obj = bpy.context.active_object
            
            return {
                "status": "success", 
                "result": {
                    "name": obj.name,
                    "location": [obj.location.x, obj.location.y, obj.location.z]
                }
            }
        except Exception as e:
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def delete_object(self, obj_name):
        try:
            # Find the object
            if obj_name in bpy.data.objects:
                obj = bpy.data.objects[obj_name]
                bpy.data.objects.remove(obj, do_unlink=True)
                return {"status": "success", "result": f"Object {obj_name} deleted"}
            else:
                return {"status": "error", "message": f"Object {obj_name} not found"}
        except Exception as e:
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def set_material(self, obj_name, color):
        try:
            if obj_name not in bpy.data.objects:
                return {"status": "error", "message": f"Object {obj_name} not found"}
                
            obj = bpy.data.objects[obj_name]
            
            # Create a new material
            mat_name = f"{obj_name}_material"
            mat = bpy.data.materials.new(name=mat_name)
            mat.use_nodes = True
            
            # Get the principled BSDF node
            principled_bsdf = mat.node_tree.nodes.get('Principled BSDF')
            if principled_bsdf:
                # Set the color
                principled_bsdf.inputs[0].default_value = color
                
            # Assign the material to the object
            if obj.data.materials:
                obj.data.materials[0] = mat
            else:
                obj.data.materials.append(mat)
                
            return {"status": "success", "result": f"Material applied to {obj_name}"}
        except Exception as e:
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def watsonx_query(self, prompt):
        """
        Send a query to WatsonX API and return the response
        """
        if not self.api_key or not self.project_id:
            return {"status": "error", "message": "WatsonX API key or project ID not configured"}
            
        try:
            # Set up the parameters for WatsonX API
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            # Different models may require different parameter sets
            payload = {
                "model_id": self.model_id,
                "input": prompt,
                "parameters": {
                    "decoding_method": "sample",
                    "max_new_tokens": 250,
                    "min_new_tokens": 1,
                    "temperature": 0.7,
                    "top_k": 50,
                    "top_p": 1
                },
                "project_id": self.project_id
            }
            
            # The endpoint will depend on your WatsonX setup
            endpoint = f"{self.url}/ml/v1/generation"
            
            # Send the request to WatsonX API
            response = requests.post(endpoint, headers=headers, json=payload)
            response.raise_for_status()  # Raise an exception for HTTP errors
            
            # Parse the response
            result = response.json()
            
            # Format for return
            return {
                "status": "success", 
                "result": {
                    "generated_text": result.get("results", [{}])[0].get("generated_text", ""),
                    "model_id": self.model_id
                }
            }
        except requests.exceptions.RequestException as e:
            return {"status": "error", "message": f"WatsonX API request failed: {str(e)}"}
        except Exception as e:
            traceback.print_exc()
            return {"status": "error", "message": f"Error in WatsonX query: {str(e)}"}

# UI Panel for Blender
class BLENDERWATSONX_PT_Panel(bpy.types.Panel):
    bl_label = "Blender WatsonX"
    bl_idname = "BLENDERWATSONX_PT_Panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'BlenderWatsonX'
    
    def draw(self, context):
        layout = self.layout
        scene = context.scene
        
        layout.prop(scene, "blenderwatsonx_port")
        
        # WatsonX API settings
        box = layout.box()
        box.label(text="WatsonX API Settings")
        box.prop(scene, "blenderwatsonx_api_key", text="API Key")
        box.prop(scene, "blenderwatsonx_project_id", text="Project ID")
        box.prop(scene, "blenderwatsonx_url", text="API URL")
        box.prop(scene, "blenderwatsonx_model_id", text="Model ID")
        
        if not scene.blenderwatsonx_server_running:
            layout.operator("blenderwatsonx.start_server", text="Start MCP Server")
        else:
            layout.operator("blenderwatsonx.stop_server", text="Stop MCP Server")

# Operator to start the server
class BLENDERWATSONX_OT_StartServer(bpy.types.Operator):
    bl_idname = "blenderwatsonx.start_server"
    bl_label = "Start BlenderWatsonX MCP Server"
    bl_description = "Start the MCP server for WatsonX integration"
    
    def execute(self, context):
        scene = context.scene
        port = scene.blenderwatsonx_port
        
        # Create a new server instance
        # We'll store it in a module-level variable instead of the scene
        global server_instance
        server = BlenderWatsonXServer(port=port)
        server_instance = server
        
        # Set the WatsonX API configuration
        server.api_key = scene.blenderwatsonx_api_key
        server.project_id = scene.blenderwatsonx_project_id
        server.url = scene.blenderwatsonx_url
        server.model_id = scene.blenderwatsonx_model_id
        
        # Start the server
        server.start()
        
        # Update UI state
        scene.blenderwatsonx_server_running = True
        
        return {'FINISHED'}

# Operator to stop the server
class BLENDERWATSONX_OT_StopServer(bpy.types.Operator):
    bl_idname = "blenderwatsonx.stop_server"
    bl_label = "Stop BlenderWatsonX MCP Server"
    bl_description = "Stop the MCP server for WatsonX integration"
    
    def execute(self, context):
        global server_instance
        if server_instance:
            server_instance.stop()
            server_instance = None
        
        # Update UI state
        context.scene.blenderwatsonx_server_running = False
        
        return {'FINISHED'}

# Registration and initialization
classes = (
    BLENDERWATSONX_PT_Panel,
    BLENDERWATSONX_OT_StartServer,
    BLENDERWATSONX_OT_StopServer,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)
    
    # Register properties
    bpy.types.Scene.blenderwatsonx_server_running = BoolProperty(
        name="Server Running",
        default=False
    )
    
    bpy.types.Scene.blenderwatsonx_port = IntProperty(
        name="Port",
        default=9876,
        min=1024,
        max=65535
    )
    
    bpy.types.Scene.blenderwatsonx_api_key = StringProperty(
        name="WatsonX API Key",
        default="",
        description="API Key for IBM WatsonX"
    )
    
    bpy.types.Scene.blenderwatsonx_project_id = StringProperty(
        name="WatsonX Project ID",
        default="",
        description="Project ID for IBM WatsonX"
    )
    
    bpy.types.Scene.blenderwatsonx_url = StringProperty(
        name="WatsonX URL",
        default="https://us-south.ml.cloud.ibm.com",
        description="URL for IBM WatsonX API"
    )
    
    bpy.types.Scene.blenderwatsonx_model_id = StringProperty(
        name="WatsonX Model ID",
        default="meta-llama/llama-3-2-90b-vision-instruct",
        description="Model ID for IBM WatsonX"
    )

def unregister():
    # Stop the server if it's running
    global server_instance
    if server_instance:
        server_instance.stop()
        server_instance = None
    
    # Unregister properties
    del bpy.types.Scene.blenderwatsonx_server_running
    del bpy.types.Scene.blenderwatsonx_port
    del bpy.types.Scene.blenderwatsonx_api_key
    del bpy.types.Scene.blenderwatsonx_project_id
    del bpy.types.Scene.blenderwatsonx_url
    del bpy.types.Scene.blenderwatsonx_model_id
    
    # Unregister classes
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)

if __name__ == "__main__":
    register()